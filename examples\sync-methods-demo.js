/**
 * Sync Methods Demo
 * Demonstrates the new sync functions in the RWAirdrop contract
 * 
 * This example shows:
 * - syncMyDomains: Domain holders sync all their domains at once
 * - syncDomainOwnershipsAdmin: Emergency/admin-only function for data recovery
 * 
 * Usage: node examples/sync-methods-demo.js
 */

// Load environment variables
require('dotenv').config();

const ODudeSDK = require('../src/index');
const { ethers } = require('ethers');

// ==================== CONFIGURATION VARIABLES ====================

const DEMO_MODE = 'user'; // 'user' or 'admin' - determines which function to demonstrate

// ==================== MAIN FUNCTION ====================

async function main() {
  console.log('=== Sync Methods Demo ===\n');

  const sdk = new ODudeSDK({
    rpcUrl_sepolia: process.env.BASE_SEPOLIA_RPC_URL || 'https://sepolia.base.org',
    privateKey: process.env.PRIVATE_KEY
  });

  try {
    sdk.connectNetwork('basesepolia');
    console.log('✅ Connected to Base Sepolia network');
  } catch (error) {
    console.log('❌ Failed to connect:', error.message);
    return;
  }

  // Get wallet address
  const walletAddress = sdk.signer?.address;
  if (!walletAddress) {
    console.log('❌ No wallet connected. Please provide a private key in .env file');
    return;
  }

  console.log(`\n🔍 Wallet address: ${walletAddress}`);

  if (DEMO_MODE === 'user') {
    await demonstrateSyncMyDomains(sdk, walletAddress);
  } else if (DEMO_MODE === 'admin') {
    await demonstrateSyncDomainOwnershipsAdmin(sdk, walletAddress);
  } else {
    console.log('❌ Invalid DEMO_MODE. Use "user" or "admin"');
  }

  console.log('\n✓ Demo completed!');
}

// ==================== USER SYNC DEMO ====================

async function demonstrateSyncMyDomains(sdk, walletAddress) {
  console.log('\n--- Demonstrating syncMyDomains Function ---');
  console.log('📝 This function allows domain holders to sync all their domains at once');
  
  // Get domains owned by the wallet
  let ownedDomains = [];
  try {
    const namesList = await sdk.getNamesList(walletAddress);
    console.log(`\n📋 Domains owned by wallet: ${namesList.length}`);
    
    if (namesList.length === 0) {
      console.log('❌ No domains found. Please mint some domains first.');
      console.log('💡 Use examples/TLDMint.js or examples/SubNameMint.js to mint domains');
      return;
    }

    namesList.forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.name} (Token ID: ${item.tokenId})`);
    });
    
    ownedDomains = namesList.map(item => item.name);
  } catch (error) {
    console.log('❌ Error getting owned domains:', error.message);
    return;
  }

  // Check current sync status
  console.log('\n--- Current Sync Status ---');
  const tldGroups = {};
  ownedDomains.forEach(domain => {
    const tld = domain.split('@')[1];
    if (!tldGroups[tld]) tldGroups[tld] = [];
    tldGroups[tld].push(domain);
  });

  for (const [tld, domains] of Object.entries(tldGroups)) {
    try {
      const syncedDomains = await sdk.rwairdrop().getUserDomainsInTLD(walletAddress, tld);
      console.log(`📝 '${tld}' TLD: ${syncedDomains.length}/${domains.length} domains synced`);
      
      const unsynced = domains.filter(d => !syncedDomains.includes(d));
      if (unsynced.length > 0) {
        console.log(`  ⚠️  Unsynced: ${unsynced.join(', ')}`);
      }
    } catch (error) {
      console.log(`❌ Error checking '${tld}' TLD:`, error.message);
    }
  }

  // Perform sync
  console.log('\n--- Performing Sync ---');
  try {
    console.log(`🔄 Calling syncMyDomains with ${ownedDomains.length} domains...`);
    console.log(`📝 Domains: ${ownedDomains.join(', ')}`);
    
    const syncTx = await sdk.rwairdrop().syncMyDomains(ownedDomains);
    console.log(`📝 Transaction hash: ${syncTx.hash}`);
    
    console.log('⏳ Waiting for confirmation...');
    const receipt = await syncTx.wait();
    console.log(`✅ Sync completed! Block: ${receipt.blockNumber}`);
    console.log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);

    // Verify sync
    console.log('\n--- Verification ---');
    for (const [tld, domains] of Object.entries(tldGroups)) {
      try {
        const syncedDomains = await sdk.rwairdrop().getUserDomainsInTLD(walletAddress, tld);
        console.log(`✅ '${tld}' TLD: ${syncedDomains.length}/${domains.length} domains synced`);
        syncedDomains.forEach((domain, index) => {
          console.log(`  ${index + 1}. ${domain}`);
        });
      } catch (error) {
        console.log(`❌ Error verifying '${tld}' TLD:`, error.message);
      }
    }

    // Check claimable airdrops
    const claimableAirdrops = await sdk.rwairdrop().getClaimableAirdrops(walletAddress);
    console.log(`\n🎁 Claimable airdrops: ${claimableAirdrops.length}`);
    
  } catch (error) {
    console.log('❌ Sync failed:', error.message);
    
    if (error.message.includes('Empty domain array')) {
      console.log('💡 No domains provided for synchronization');
    } else if (error.message.includes('Not owner of all domains')) {
      console.log('💡 You can only sync domains that you own');
    } else if (error.message.includes('insufficient funds')) {
      console.log('💡 Make sure the wallet has enough ETH for gas fees');
    }
  }
}

// ==================== ADMIN SYNC DEMO ====================

async function demonstrateSyncDomainOwnershipsAdmin(sdk, walletAddress) {
  console.log('\n--- Demonstrating syncDomainOwnershipsAdmin Function ---');
  console.log('📝 This is an admin-only function for emergency data recovery');
  console.log('⚠️  Only the contract owner can call this function');

  // Check if current wallet is the contract owner
  try {
    const contractOwner = await sdk.rwairdrop().owner();
    console.log(`\n🔍 Contract owner: ${contractOwner}`);
    console.log(`🔍 Current wallet: ${walletAddress}`);

    if (contractOwner.toLowerCase() !== walletAddress.toLowerCase()) {
      console.log('❌ Current wallet is not the contract owner');
      console.log('💡 This function can only be called by the contract owner');
      console.log('💡 Switch to admin mode by setting DEMO_MODE to "user" to see syncMyDomains instead');
      return;
    }

    console.log('✅ Current wallet is the contract owner - can use admin functions');
  } catch (error) {
    console.log('❌ Error checking contract owner:', error.message);
    return;
  }

  // Example admin sync (you would replace these with actual domain names and owners)
  const exampleDomains = ['admin-test@xxx', 'recovery@xxx'];
  const exampleOwners = [
    '******************************************', // Example owner 1
    '******************************************'  // Example owner 2
  ];

  console.log('\n--- Admin Sync Example ---');
  console.log('📝 This example shows how to sync domain ownership for data recovery');
  console.log(`📝 Domains to sync: ${exampleDomains.join(', ')}`);
  console.log(`📝 Owners: ${exampleOwners.join(', ')}`);

  try {
    console.log('\n🔄 Calling syncDomainOwnershipsAdmin...');

    const syncTx = await sdk.rwairdrop().syncDomainOwnershipsAdmin(exampleDomains, exampleOwners);
    console.log(`📝 Transaction hash: ${syncTx.hash}`);

    console.log('⏳ Waiting for confirmation...');
    const receipt = await syncTx.wait();
    console.log(`✅ Admin sync completed! Block: ${receipt.blockNumber}`);
    console.log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);

    // Verify the sync
    console.log('\n--- Verification ---');
    for (let i = 0; i < exampleDomains.length; i++) {
      const domain = exampleDomains[i];
      const expectedOwner = exampleOwners[i];
      const tld = domain.split('@')[1];

      try {
        const userDomains = await sdk.rwairdrop().getUserDomainsInTLD(expectedOwner, tld);
        const isSynced = userDomains.includes(domain);
        console.log(`${isSynced ? '✅' : '❌'} ${domain} -> ${expectedOwner}: ${isSynced ? 'Synced' : 'Not synced'}`);
      } catch (error) {
        console.log(`❌ Error verifying ${domain}:`, error.message);
      }
    }

  } catch (error) {
    console.log('❌ Admin sync failed:', error.message);

    if (error.message.includes('Ownable: caller is not the owner')) {
      console.log('💡 Only the contract owner can call this function');
    } else if (error.message.includes('Array length mismatch')) {
      console.log('💡 Domain names and owners arrays must have the same length');
    } else if (error.message.includes('insufficient funds')) {
      console.log('💡 Make sure the wallet has enough ETH for gas fees');
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error:', error);
    process.exit(1);
  });
