/**
 * Sync Domain Ownership
 * Demonstrates the new syncMyDomains function for synchronizing domain ownership
 * between Registry and RWAirdrop contracts
 *
 * This example shows:
 * - Using the new syncMyDomains function (replaces old syncDomainOwnership)
 * - Checking domain ownership before and after sync
 * - Verifying claimable airdrops after synchronization
 */

require('dotenv').config();
const ODudeSDK = require('../src/index');

async function main() {
  console.log('=== Sync Domain Ownership ===\n');

  const sdk = new ODudeSDK({
    rpcUrl_sepolia: process.env.BASE_SEPOLIA_RPC_URL || 'https://sepolia.base.org',
    privateKey: process.env.PRIVATE_KEY
  });

  try {
    sdk.connectNetwork('basesepolia');
    console.log('✅ Connected to Base Sepolia network');
  } catch (error) {
    console.log('❌ Failed to connect:', error.message);
    return;
  }

  // Get wallet address from private key
  const walletAddress = sdk.signer?.address;
  if (!walletAddress) {
    console.log('❌ No wallet connected. Please provide a private key in .env file');
    return;
  }

  console.log(`\n🔍 Wallet address: ${walletAddress}`);
  console.log(`🔍 Checking domains owned by this wallet...`);

  // Check Registry contract
  console.log('\n--- Registry Contract ---');
  let ownedDomains = [];
  try {
    const namesList = await sdk.getNamesList(walletAddress);
    console.log(`📝 Names in Registry: ${namesList.length}`);
    namesList.forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.name} (Token ID: ${item.tokenId})`);
    });
    ownedDomains = namesList.map(item => item.name);
  } catch (error) {
    console.log('❌ Error getting names from Registry:', error.message);
  }

  // Check RWAirdrop contract for each TLD
  console.log('\n--- RWAirdrop Contract ---');
  const tldDomains = {};

  // Group domains by TLD
  ownedDomains.forEach(domain => {
    const tld = domain.split('@')[1];
    if (!tldDomains[tld]) {
      tldDomains[tld] = [];
    }
    tldDomains[tld].push(domain);
  });

  for (const [tld, domains] of Object.entries(tldDomains)) {
    try {
      const userDomains = await sdk.rwairdrop().getUserDomainsInTLD(walletAddress, tld);
      console.log(`📝 Domains in '${tld}' TLD (RWAirdrop): ${userDomains.length}`);
      if (userDomains.length > 0) {
        userDomains.forEach((domain, index) => {
          console.log(`  ${index + 1}. ${domain}`);
        });
      }
    } catch (error) {
      console.log(`❌ Error getting domains from RWAirdrop for '${tld}':`, error.message);
    }
  }

  // Sync domain ownership using the new syncMyDomains function
  console.log('\n--- Synchronizing Domain Ownership (New Method) ---');

  if (ownedDomains.length === 0) {
    console.log('❌ No domains found to sync');
    return;
  }

  try {
    console.log(`🔄 Syncing ${ownedDomains.length} domains using syncMyDomains...`);
    console.log(`📝 Domains to sync: ${ownedDomains.join(', ')}`);

    const syncTx = await sdk.rwairdrop().syncMyDomains(ownedDomains);
    console.log(`📝 Transaction hash: ${syncTx.hash}`);

    console.log('⏳ Waiting for transaction confirmation...');
    const receipt = await syncTx.wait();
    console.log(`✅ Transaction confirmed in block ${receipt.blockNumber}`);
    console.log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);

    // Verify synchronization worked
    console.log('\n--- Verification After Sync ---');
    for (const [tld, domains] of Object.entries(tldDomains)) {
      try {
        const userDomainsAfter = await sdk.rwairdrop().getUserDomainsInTLD(walletAddress, tld);
        console.log(`📝 Domains in '${tld}' TLD after sync: ${userDomainsAfter.length}`);
        if (userDomainsAfter.length > 0) {
          userDomainsAfter.forEach((domain, index) => {
            console.log(`  ${index + 1}. ${domain}`);
          });
        }
      } catch (error) {
        console.log(`❌ Error verifying sync for '${tld}':`, error.message);
      }
    }

    // Check claimable airdrops after sync
    const claimableAirdrops = await sdk.rwairdrop().getClaimableAirdrops(walletAddress);
    console.log(`\n🎁 Claimable airdrops after sync: ${claimableAirdrops.length}`);

    if (claimableAirdrops.length > 0) {
      console.log('🎉 SUCCESS! Domains are now synchronized and airdrops are claimable!');
      claimableAirdrops.forEach((airdrop, index) => {
        console.log(`  🎁 Airdrop ${index + 1}: ${airdrop.tldName} (ID: ${airdrop.airdropId})`);
      });
    } else {
      console.log('⚠️  Domains synchronized but no claimable airdrops found');
    }

  } catch (error) {
    console.log('❌ Error syncing domain ownership:', error.message);

    if (error.message.includes('insufficient funds')) {
      console.log('💡 Make sure the wallet has enough ETH for gas fees');
    } else if (error.message.includes('Empty domain array')) {
      console.log('💡 No domains provided for synchronization');
    } else if (error.message.includes('Not owner of all domains')) {
      console.log('💡 You can only sync domains that you own');
    } else {
      console.log('💡 Make sure you own the domains you are trying to sync');
    }
  }

  console.log('\n✓ Sync attempt completed!');
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error:', error);
    process.exit(1);
  });
