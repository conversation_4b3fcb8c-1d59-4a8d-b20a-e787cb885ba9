/**
 * Claim Airdrop Example
 * Demonstrates how to claim airdrop shares for domains owned by the current wallet
 *
 * This example shows:
 * - Connecting to Base Sepolia network with private key
 * - Getting wallet address from private key
 * - Checking for domains owned by the wallet in the specified TLD
 * - Finding claimable airdrops for owned domains
 * - Claiming airdrop shares
 * - Displaying airdrop creator information
 *
 * Usage: node examples/claim-airdrop.js
 */

// Load environment variables
require('dotenv').config();

const ODudeSDK = require('../src/index');
const { ethers } = require('ethers');

// ==================== CONFIGURATION VARIABLES ====================
// Update these variables to claim from different TLDs

const TLD_NAME = 'xxx'; // TLD to check for owned domains and claim airdrops

// ==================== ERC20 TOKEN ABI ====================
const ERC20_ABI = [
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function decimals() view returns (uint8)',
  'function balanceOf(address owner) view returns (uint256)'
];

async function main() {
  console.log('=== ODude Airdrop Claim Example ===\n');

  // Check for required environment variables
  if (!process.env.PRIVATE_KEY) {
    console.error('❌ Error: PRIVATE_KEY not found in .env file');
    console.log('Please add your private key to the .env file:');
    console.log('PRIVATE_KEY=your_private_key_here');
    process.exit(1);
  }

  console.log('📋 Configuration:');
  console.log(`  TLD Name: ${TLD_NAME}`);
  console.log();

  // Initialize SDK with Base Sepolia configuration and private key
  const sdk = new ODudeSDK({
    rpcUrl_sepolia: process.env.BASE_SEPOLIA_RPC_URL || 'https://sepolia.base.org',
    privateKey: process.env.PRIVATE_KEY
  });

  try {
    // Connect to Base Sepolia network (the only working network currently)
    sdk.connectNetwork('basesepolia');
    console.log('✅ Connected to Base Sepolia network');
  } catch (error) {
    console.error('❌ Failed to connect to Base Sepolia:', error.message);
    console.log('Please check your BASE_SEPOLIA_RPC_URL in the .env file');
    process.exit(1);
  }

  // Get wallet address from the signer
  const signer = sdk.getSigner('basesepolia');
  const walletAddress = signer.address;
  console.log(`📱 Current Wallet Address: ${walletAddress}\n`);

  // Get provider for token contract interactions
  const provider = sdk.getProvider('basesepolia');

  // Helper function to get token information
  async function getTokenInfo(tokenAddress) {
    try {
      const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, provider);
      const [name, symbol, decimals] = await Promise.all([
        tokenContract.name(),
        tokenContract.symbol(),
        tokenContract.decimals()
      ]);

      return {
        name,
        symbol,
        decimals: Number(decimals),
        contract: tokenContract
      };
    } catch (error) {
      console.log(`⚠️  Could not fetch token information for ${tokenAddress}:`, error.message);
      return {
        name: 'Unknown',
        symbol: 'UNK',
        decimals: 18,
        contract: null
      };
    }
  }

  console.log(`--- Checking Domains in "${TLD_NAME}" TLD ---`);

  let userDomains = [];
  try {
    // First try the RWAirdrop method (may not work correctly)
    console.log('🔍 Trying getUserDomainsInTLD method...');
    const rwairdropDomains = await sdk.rwairdrop().getUserDomainsInTLD(walletAddress, TLD_NAME);
    console.log(`   Result: ${rwairdropDomains.length} domains found`);

    if (rwairdropDomains.length > 0) {
      userDomains = rwairdropDomains;
    } else {
      // Fallback: Use getNamesList and filter for the TLD
      console.log('🔍 Fallback: Using getNamesList method...');
      const allNames = await sdk.getNamesList(walletAddress);
      userDomains = allNames
        .map(nameInfo => nameInfo.name)
        .filter(name => name.endsWith(`@${TLD_NAME}`));
      console.log(`   Result: ${userDomains.length} domains found in ${TLD_NAME} TLD`);
    }

    console.log(`✓ Found ${userDomains.length} domains owned by your wallet in '${TLD_NAME}' TLD`);

    if (userDomains.length > 0) {
      console.log('📝 Your domains:');
      userDomains.forEach((domain, index) => {
        console.log(`  ${index + 1}. ${domain}`);
      });
    } else {
      console.log(`❌ You don't own any domains in the "${TLD_NAME}" TLD`);
      console.log('💡 To be eligible for airdrops, you need to own domains in the TLD');
      console.log('💡 Use the SubNameMint.js example to mint a domain first');
      process.exit(0);
    }
  } catch (error) {
    console.error('❌ Failed to get user domains:', error.message);
    process.exit(1);
  }

  console.log(`\n--- Checking Available Airdrops for "${TLD_NAME}" TLD ---`);
  
  let airdropCount = 0;
  try {
    // Get airdrop count for the specified TLD
    airdropCount = await sdk.rwairdrop().getTLDAirdropCount(TLD_NAME);
    console.log(`✓ Total airdrops available for "${TLD_NAME}" TLD: ${airdropCount.toString()}`);
    
    if (airdropCount === 0) {
      console.log(`❌ No airdrops found for "${TLD_NAME}" TLD`);
      console.log('💡 Create an airdrop using the test-airdrop.js example first');
      process.exit(0);
    }
  } catch (error) {
    console.error('❌ Failed to get airdrop count:', error.message);
    process.exit(1);
  }

  console.log('\n--- Syncing Domain Ownership ---');

  // Sync domain ownership to RWAirdrop contract if needed
  for (const domain of userDomains) {
    try {
      console.log(`🔄 Syncing ${domain} ownership to RWAirdrop contract...`);
      const syncTx = await sdk.rwairdrop().syncDomainOwnership(domain, walletAddress);
      console.log(`  📝 Sync transaction: ${syncTx.hash}`);

      const receipt = await syncTx.wait();
      console.log(`  ✅ Sync confirmed! Block: ${receipt.blockNumber}`);
    } catch (error) {
      if (error.message.includes('already synced') || error.message.includes('already exists')) {
        console.log(`  ✅ ${domain} already synced`);
      } else {
        console.log(`  ⚠️  Sync failed for ${domain}: ${error.message}`);
      }
    }
  }

  console.log('\n--- Checking Claimable Airdrops ---');

  try {
    // Get claimable airdrops for the current wallet (after sync)
    const claimableAirdrops = await sdk.rwairdrop().getClaimableAirdrops(walletAddress);
    console.log(`✓ You have ${claimableAirdrops.length} claimable airdrops`);
    
    if (claimableAirdrops.length === 0) {
      console.log('❌ No claimable airdrops found for your wallet');
      console.log('💡 This could mean:');
      console.log('  - You have already claimed all available airdrops');
      console.log('  - No airdrops are active for your domains');
      console.log('  - Your domains are not eligible for current airdrops');
      
      // Still show detailed status for each domain
      console.log('\n--- Detailed Domain Status ---');
      for (const domain of userDomains) {
        console.log(`\n🏷️  Domain: ${domain}`);
        
        for (let airdropIndex = 0; airdropIndex < airdropCount; airdropIndex++) {
          const hasClaimed = await sdk.rwairdrop().hasDomainClaimed(domain, airdropIndex);
          const claimedAmount = await sdk.rwairdrop().getDomainClaimedAmount(domain, airdropIndex);
          
          // Get airdrop info
          const airdropInfo = await sdk.rwairdrop().getAirdropInfoByTLD(TLD_NAME, airdropIndex);
          const tokenInfo = await getTokenInfo(airdropInfo.tokenAddress);
          
          console.log(`  📦 Airdrop ${airdropIndex}:`);
          console.log(`    Token: ${airdropInfo.tokenAddress} (${tokenInfo.symbol})`);
          console.log(`    Has claimed: ${hasClaimed ? '✅ Yes' : '❌ No'}`);
          console.log(`    Claimed amount: ${ethers.formatUnits(claimedAmount, tokenInfo.decimals)} ${tokenInfo.symbol}`);
          console.log(`    Airdrop active: ${airdropInfo.isActive ? '✅ Yes' : '❌ No'}`);
        }
      }
      
      process.exit(0);
    }

    // Display claimable airdrops
    console.log('\n🎁 Claimable Airdrops:');
    for (let index = 0; index < claimableAirdrops.length; index++) {
      const airdrop = claimableAirdrops[index];
      console.log(`\n  ${index + 1}. Airdrop Details:`);
      console.log(`    TLD: ${airdrop.tldName}`);
      console.log(`    Airdrop ID: ${airdrop.airdropId.toString()}`);
      console.log(`    Token Address: ${airdrop.tokenAddress}`);

      // Get token info for this airdrop
      const tokenInfo = await getTokenInfo(airdrop.tokenAddress);
      console.log(`    Token Name: ${tokenInfo.name}`);
      console.log(`    Token Symbol: ${tokenInfo.symbol}`);
      console.log(`    Per User Share: ${ethers.formatUnits(airdrop.perUserShare, tokenInfo.decimals)} ${tokenInfo.symbol}`);
      console.log(`    Can Claim: ${airdrop.canClaim ? '✅ Yes' : '❌ No'}`);
    }
  } catch (error) {
    console.error('❌ Failed to get claimable airdrops:', error.message);
    process.exit(1);
  }

  console.log('\n--- Airdrop Creator Information ---');

  try {
    // Get detailed airdrop information including creator
    for (let airdropIndex = 0; airdropIndex < airdropCount; airdropIndex++) {
      const airdropInfo = await sdk.rwairdrop().getAirdropInfoByTLD(TLD_NAME, airdropIndex);
      const tokenInfo = await getTokenInfo(airdropInfo.tokenAddress);

      console.log(`\n📦 Airdrop ${airdropIndex} Information:`);
      console.log(`  Token: ${airdropInfo.tokenAddress} (${tokenInfo.name} - ${tokenInfo.symbol})`);
      console.log(`  Total Amount: ${ethers.formatUnits(airdropInfo.totalAmount, tokenInfo.decimals)} ${tokenInfo.symbol}`);
      console.log(`  Per User Share: ${ethers.formatUnits(airdropInfo.perUserShare, tokenInfo.decimals)} ${tokenInfo.symbol}`);
      console.log(`  Remaining Balance: ${ethers.formatUnits(airdropInfo.remainingBalance, tokenInfo.decimals)} ${tokenInfo.symbol}`);
      console.log(`  Is Active: ${airdropInfo.isActive ? '✅ Yes' : '❌ No'}`);
      console.log(`  Is Withdrawn: ${airdropInfo.isWithdrawn ? '✅ Yes' : '❌ No'}`);

      // Note: The smart contract doesn't currently store creator information
      // This would need to be added to the contract if required
      console.log(`  💡 Creator info: Not available in current contract version`);
      console.log(`     (Consider adding creator tracking to smart contract)`);
    }
  } catch (error) {
    console.error('❌ Failed to get airdrop information:', error.message);
  }

  console.log('\n--- Claiming Airdrop Shares ---');

  let claimsSuccessful = 0;
  let totalClaimsAttempted = 0;

  // Process each domain and attempt to claim from available airdrops
  for (const domain of userDomains) {
    console.log(`\n🏷️  Processing domain: ${domain}`);

    for (let airdropIndex = 0; airdropIndex < airdropCount; airdropIndex++) {
      try {
        // Check if this domain has already claimed from this airdrop
        const hasClaimed = await sdk.rwairdrop().hasDomainClaimed(domain, airdropIndex);

        if (hasClaimed) {
          console.log(`  📦 Airdrop ${airdropIndex}: ✅ Already claimed`);
          continue;
        }

        // Get airdrop info to check if it's active
        const airdropInfo = await sdk.rwairdrop().getAirdropInfoByTLD(TLD_NAME, airdropIndex);

        if (!airdropInfo.isActive) {
          console.log(`  📦 Airdrop ${airdropIndex}: ❌ Not active`);
          continue;
        }

        if (airdropInfo.remainingBalance === 0n) {
          console.log(`  📦 Airdrop ${airdropIndex}: ❌ No remaining balance`);
          continue;
        }

        // Get token info for display
        const tokenInfo = await getTokenInfo(airdropInfo.tokenAddress);

        console.log(`  📦 Airdrop ${airdropIndex}: 🚀 Attempting to claim...`);
        console.log(`    Token: ${tokenInfo.symbol}`);
        console.log(`    Claimable Amount: ${ethers.formatUnits(airdropInfo.perUserShare, tokenInfo.decimals)} ${tokenInfo.symbol}`);

        totalClaimsAttempted++;

        // Get balance before claim
        let balanceBefore = 0n;
        if (tokenInfo.contract) {
          balanceBefore = await tokenInfo.contract.balanceOf(walletAddress);
        }

        // Attempt to claim the airdrop share
        const claimTx = await sdk.rwairdrop().claimShare(TLD_NAME, airdropIndex, domain);
        console.log(`    📝 Claim transaction: ${claimTx.hash}`);
        console.log(`    ⏳ Waiting for confirmation...`);

        const receipt = await claimTx.wait();
        console.log(`    ✅ Claim confirmed! Block: ${receipt.blockNumber}`);
        console.log(`    ⛽ Gas used: ${receipt.gasUsed.toString()}`);

        // Check balance after claim
        if (tokenInfo.contract) {
          const balanceAfter = await tokenInfo.contract.balanceOf(walletAddress);
          const received = balanceAfter - balanceBefore;
          console.log(`    💰 Tokens received: ${ethers.formatUnits(received, tokenInfo.decimals)} ${tokenInfo.symbol}`);
        }

        claimsSuccessful++;

      } catch (error) {
        console.log(`  📦 Airdrop ${airdropIndex}: ❌ Claim failed - ${error.message}`);

        // Check if it's a known error condition
        if (error.message.includes('already claimed')) {
          console.log(`    💡 This domain has already claimed from this airdrop`);
        } else if (error.message.includes('not active')) {
          console.log(`    💡 This airdrop is not currently active`);
        } else if (error.message.includes('insufficient balance')) {
          console.log(`    💡 Airdrop has insufficient remaining balance`);
        } else {
          console.log(`    💡 Unexpected error - check transaction details`);
        }
      }
    }
  }

  console.log('\n=== Claim Summary ===');
  console.log(`✅ Successful claims: ${claimsSuccessful}`);
  console.log(`📊 Total attempts: ${totalClaimsAttempted}`);
  console.log(`📱 Wallet address: ${walletAddress}`);
  console.log(`🏷️  Domains processed: ${userDomains.length}`);
  console.log(`📦 Airdrops checked: ${airdropCount}`);

  if (claimsSuccessful > 0) {
    console.log('\n🎉 Congratulations! You have successfully claimed airdrop shares!');
    console.log('💡 Check your wallet balance to see the received tokens');
  } else if (totalClaimsAttempted === 0) {
    console.log('\n💡 No claims were attempted - all airdrops may have been already claimed');
  } else {
    console.log('\n⚠️  Some claims failed - check the error messages above for details');
  }

  console.log('\n✓ Claim process completed!');
}

// Run the main function
main()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Script failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  });
